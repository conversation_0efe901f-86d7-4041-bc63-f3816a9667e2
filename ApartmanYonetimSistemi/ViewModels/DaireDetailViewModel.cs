using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using Serilog;
using System;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class DaireDetailViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;

        [ObservableProperty]
        private Daire? currentDaire;

        [ObservableProperty]
        private ObservableCollection<Payment> odemeGecmisi = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private bool hasKiraci = false;

        [ObservableProperty]
        private string daireTitle = string.Empty;

        public DaireDetailViewModel()
        {
            _firebaseService = FirebaseService.Instance;
        }

        public void Initialize(Daire daire)
        {
            CurrentDaire = daire;
            HasKiraci = daire.Kiraci != null;
            DaireTitle = $"Daire {daire.DaireNo}";
            _ = LoadOdemeGecmisiAsync();
        }

        [RelayCommand]
        private async Task LoadOdemeGecmisiAsync()
        {
            if (CurrentDaire == null) return;

            IsLoading = true;
            try
            {
                OdemeGecmisi.Clear();

                var paymentsCollection = _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(GetSiteIdFromDaire())
                    .Collection("Apartments")
                    .Document(CurrentDaire.ApartmentId)
                    .Collection("Daireler")
                    .Document(CurrentDaire.Id)
                    .Collection("Payments");

                var snapshot = await paymentsCollection
                    .OrderByDescending("Tarih")
                    .GetSnapshotAsync();

                foreach (var document in snapshot.Documents)
                {
                    var payment = document.ConvertTo<Payment>();
                    payment.Id = document.Id;
                    payment.DaireId = CurrentDaire.Id;
                    OdemeGecmisi.Add(payment);
                }

                Log.Information("Ödeme geçmişi yüklendi: {Count} ödeme", OdemeGecmisi.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Ödeme geçmişi yüklenirken hata oluştu");
                MessageBox.Show($"Ödeme geçmişi yüklenirken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void AddKiraci()
        {
            if (CurrentDaire == null) return;

            var kiraciWindow = new Views.KiraciEditWindow();
            if (kiraciWindow.DataContext is KiraciEditViewModel vm)
            {
                vm.Initialize(CurrentDaire, null); // null = yeni kiracı
                vm.KiraciSaved += OnKiraciSaved;
            }
            kiraciWindow.ShowDialog();
        }

        [RelayCommand]
        private void EditKiraci()
        {
            if (CurrentDaire?.Kiraci == null) return;

            var kiraciWindow = new Views.KiraciEditWindow();
            if (kiraciWindow.DataContext is KiraciEditViewModel vm)
            {
                vm.Initialize(CurrentDaire, CurrentDaire.Kiraci);
                vm.KiraciSaved += OnKiraciSaved;
            }
            kiraciWindow.ShowDialog();
        }

        [RelayCommand]
        private async Task RemoveKiraciAsync()
        {
            if (CurrentDaire?.Kiraci == null) return;

            var result = MessageBox.Show(
                $"{CurrentDaire.Kiraci.Ad} {CurrentDaire.Kiraci.Soyad} isimli kiracıyı çıkarmak istediğinizden emin misiniz?",
                "Kiracı Çıkarma", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await RemoveKiraciFromDaireAsync();
            }
        }

        [RelayCommand]
        private void AddPayment()
        {
            if (CurrentDaire == null) return;

            var paymentWindow = new Views.PaymentEditWindow();
            if (paymentWindow.DataContext is PaymentEditViewModel vm)
            {
                vm.Initialize(CurrentDaire, null); // null = yeni ödeme
                vm.PaymentSaved += OnPaymentSaved;
            }
            paymentWindow.ShowDialog();
        }

        [RelayCommand]
        private void EditPayment(Payment payment)
        {
            if (CurrentDaire == null || payment == null) return;

            var paymentWindow = new Views.PaymentEditWindow();
            if (paymentWindow.DataContext is PaymentEditViewModel vm)
            {
                vm.Initialize(CurrentDaire, payment);
                vm.PaymentSaved += OnPaymentSaved;
            }
            paymentWindow.ShowDialog();
        }

        [RelayCommand]
        private async Task UpdateDaireInfoAsync()
        {
            if (CurrentDaire == null) return;

            // Basit input dialog'ları ile kira ve aidat güncelleme
            var kiraInput = Microsoft.VisualBasic.Interaction.InputBox(
                "Yeni kira bedelini girin:", "Kira Güncelle", CurrentDaire.KiraBedeli.ToString());

            if (decimal.TryParse(kiraInput, out decimal yeniKira))
            {
                var aidatInput = Microsoft.VisualBasic.Interaction.InputBox(
                    "Yeni aidat tutarını girin:", "Aidat Güncelle", CurrentDaire.Aidat.ToString());

                if (decimal.TryParse(aidatInput, out decimal yeniAidat))
                {
                    await UpdateDaireBedellerAsync(yeniKira, yeniAidat);
                }
            }
        }

        private async Task UpdateDaireBedellerAsync(decimal yeniKira, decimal yeniAidat)
        {
            if (CurrentDaire == null) return;

            IsLoading = true;
            try
            {
                CurrentDaire.KiraBedeli = yeniKira;
                CurrentDaire.Aidat = yeniAidat;

                await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(GetSiteIdFromDaire())
                    .Collection("Apartments")
                    .Document(CurrentDaire.ApartmentId)
                    .Collection("Daireler")
                    .Document(CurrentDaire.Id)
                    .SetAsync(CurrentDaire);

                Log.Information("Daire bedelleri güncellendi: Kira={Kira}, Aidat={Aidat}", yeniKira, yeniAidat);
                MessageBox.Show("Daire bedelleri başarıyla güncellendi!", "Başarılı", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Daire bedelleri güncellenirken hata oluştu");
                MessageBox.Show($"Güncelleme sırasında hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task RemoveKiraciFromDaireAsync()
        {
            if (CurrentDaire == null) return;

            IsLoading = true;
            try
            {
                CurrentDaire.Kiraci!.CikisTarihi = DateTime.Now;
                CurrentDaire.Kiraci = null;
                HasKiraci = false;

                await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(GetSiteIdFromDaire())
                    .Collection("Apartments")
                    .Document(CurrentDaire.ApartmentId)
                    .Collection("Daireler")
                    .Document(CurrentDaire.Id)
                    .SetAsync(CurrentDaire);

                Log.Information("Kiracı daireden çıkarıldı: {DaireNo}", CurrentDaire.DaireNo);
                MessageBox.Show("Kiracı başarıyla çıkarıldı!", "Başarılı", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Kiracı çıkarılırken hata oluştu");
                MessageBox.Show($"Kiracı çıkarılırken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void OnKiraciSaved(object? sender, EventArgs e)
        {
            // Daire bilgilerini yenile
            _ = RefreshDaireAsync();
        }

        private void OnPaymentSaved(object? sender, EventArgs e)
        {
            // Ödeme geçmişini yenile
            _ = LoadOdemeGecmisiAsync();
            _ = RefreshDaireAsync(); // Ödeme durumlarını güncelle
        }

        private async Task RefreshDaireAsync()
        {
            if (CurrentDaire == null) return;

            try
            {
                var daireDoc = await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(GetSiteIdFromDaire())
                    .Collection("Apartments")
                    .Document(CurrentDaire.ApartmentId)
                    .Collection("Daireler")
                    .Document(CurrentDaire.Id)
                    .GetSnapshotAsync();

                if (daireDoc.Exists)
                {
                    var updatedDaire = daireDoc.ConvertTo<Daire>();
                    updatedDaire.Id = daireDoc.Id;
                    CurrentDaire = updatedDaire;
                    HasKiraci = updatedDaire.Kiraci != null;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Daire bilgileri yenilenirken hata oluştu");
            }
        }

        private string GetSiteIdFromDaire()
        {
            // Bu bilgiyi daire'den alamıyoruz, bu yüzden navigation parametresi olarak geçmemiz gerekiyor
            // Şimdilik basit bir çözüm kullanacağız
            return "default-site-id"; // Bu gerçek implementasyonda düzeltilecek
        }

        [RelayCommand]
        private void GoBack()
        {
            // Apartman detay ekranına dön
            foreach (Window window in Application.Current.Windows)
            {
                if (window.GetType().Name == "DaireDetailWindow")
                {
                    window.Close();
                    break;
                }
            }
        }
    }
}
