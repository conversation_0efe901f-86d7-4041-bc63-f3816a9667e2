using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Collections.ObjectModel;
using System.Threading.Tasks;
using System.Windows;
using Serilog;
using System;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class SiteDetailViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;

        [ObservableProperty]
        private Site? currentSite;

        [ObservableProperty]
        private ObservableCollection<Apartment> apartments = new();

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private Apartment? selectedApartment;

        public SiteDetailViewModel()
        {
            _firebaseService = FirebaseService.Instance;
        }

        public void Initialize(Site site)
        {
            CurrentSite = site;
            _ = LoadApartmentsAsync();
        }

        [RelayCommand]
        private async Task LoadApartmentsAsync()
        {
            if (CurrentSite == null) return;

            IsLoading = true;
            try
            {
                Apartments.Clear();

                var apartmentsCollection = _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(CurrentSite.Id)
                    .Collection("Apartments");

                var snapshot = await apartmentsCollection.GetSnapshotAsync();

                foreach (var document in snapshot.Documents)
                {
                    var apartment = document.ConvertTo<Apartment>();
                    apartment.Id = document.Id;
                    apartment.SiteId = CurrentSite.Id;
                    Apartments.Add(apartment);
                }

                Log.Information("Site apartmanları yüklendi: {Count} apartman", Apartments.Count);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Apartmanlar yüklenirken hata oluştu");
                MessageBox.Show($"Apartmanlar yüklenirken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void SelectApartment(Apartment apartment)
        {
            SelectedApartment = apartment;
            Log.Information("Apartman seçildi: {ApartmentName}", apartment.ApartmentName);
            
            // Apartman detay ekranına geçiş yapılacak
            var apartmentDetailWindow = new Views.ApartmentDetailWindow();
            if (apartmentDetailWindow.DataContext is ApartmentDetailViewModel vm)
            {
                vm.Initialize(apartment);
            }
            apartmentDetailWindow.Show();
        }

        [RelayCommand]
        private async Task CreateNewApartmentAsync()
        {
            if (CurrentSite == null) return;

            var apartmentName = Microsoft.VisualBasic.Interaction.InputBox(
                "Apartman adını girin:", "Yeni Apartman", "");

            if (!string.IsNullOrWhiteSpace(apartmentName))
            {
                await CreateApartmentAsync(apartmentName);
            }
        }

        private async Task CreateApartmentAsync(string apartmentName)
        {
            if (CurrentSite == null) return;

            IsLoading = true;
            try
            {
                var newApartment = new Apartment
                {
                    Id = Guid.NewGuid().ToString(),
                    ApartmentName = apartmentName,
                    SiteId = CurrentSite.Id
                };

                await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(CurrentSite.Id)
                    .Collection("Apartments")
                    .Document(newApartment.Id)
                    .SetAsync(newApartment);

                Apartments.Add(newApartment);
                Log.Information("Yeni apartman oluşturuldu: {ApartmentName}", apartmentName);
                
                MessageBox.Show($"'{apartmentName}' apartmanı başarıyla oluşturuldu!", "Başarılı", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Apartman oluşturulurken hata oluştu");
                MessageBox.Show($"Apartman oluşturulurken hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void GoBack()
        {
            // Ana dashboard'a dön
            var mainWindow = new MainWindow();
            mainWindow.Show();
            
            // Mevcut pencereyi kapat
            foreach (Window window in Application.Current.Windows)
            {
                if (window.GetType().Name == "SiteDetailWindow")
                {
                    window.Close();
                    break;
                }
            }
        }
    }
}
