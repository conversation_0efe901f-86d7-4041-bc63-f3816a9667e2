using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class PaymentEditViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;
        private Daire? _currentDaire;
        private Payment? _currentPayment;
        private bool _isEditMode;

        [ObservableProperty]
        private DateTime tarih = DateTime.Now;

        [ObservableProperty]
        private decimal tutar = 0;

        [ObservableProperty]
        private PaymentType selectedTip = PaymentType.Kira;

        [ObservableProperty]
        private PaymentStatus selectedOdemeDurumu = PaymentStatus.Odendi;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string windowTitle = "Yeni Ödeme";

        [ObservableProperty]
        private string saveButtonText = "Kaydet";

        public List<PaymentType> PaymentTypes { get; } = Enum.GetValues<PaymentType>().ToList();
        public List<PaymentStatus> PaymentStatuses { get; } = Enum.GetValues<PaymentStatus>().ToList();

        public event EventHandler? PaymentSaved;

        public PaymentEditViewModel()
        {
            _firebaseService = FirebaseService.Instance;
        }

        public void Initialize(Daire daire, Payment? payment)
        {
            _currentDaire = daire;
            _currentPayment = payment;
            _isEditMode = payment != null;

            if (_isEditMode && payment != null)
            {
                // Düzenleme modu
                Tarih = payment.Tarih;
                Tutar = payment.Tutar;
                SelectedTip = payment.Tip;
                SelectedOdemeDurumu = payment.OdemeDurumu;
                WindowTitle = "Ödeme Düzenle";
                SaveButtonText = "Güncelle";
            }
            else
            {
                // Yeni ödeme modu
                WindowTitle = "Yeni Ödeme";
                SaveButtonText = "Kaydet";
                Tarih = DateTime.Now;
                SelectedOdemeDurumu = PaymentStatus.Odendi;
                
                // Varsayılan tutarları daire bilgilerinden al
                if (daire != null)
                {
                    Tutar = SelectedTip == PaymentType.Kira ? daire.KiraBedeli : daire.Aidat;
                }
            }
        }

        [RelayCommand]
        private void TipChanged()
        {
            // Tip değiştiğinde varsayılan tutarı güncelle (sadece yeni ödeme modunda)
            if (!_isEditMode && _currentDaire != null)
            {
                Tutar = SelectedTip == PaymentType.Kira ? _currentDaire.KiraBedeli : _currentDaire.Aidat;
            }
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (!ValidateInput()) return;

            IsLoading = true;
            try
            {
                Payment payment;
                
                if (_isEditMode && _currentPayment != null)
                {
                    // Mevcut ödemeyi güncelle
                    payment = _currentPayment;
                    payment.Tarih = Tarih;
                    payment.Tutar = Tutar;
                    payment.Tip = SelectedTip;
                    payment.OdemeDurumu = SelectedOdemeDurumu;
                }
                else
                {
                    // Yeni ödeme oluştur
                    payment = new Payment
                    {
                        Id = Guid.NewGuid().ToString(),
                        Tarih = Tarih,
                        Tutar = Tutar,
                        Tip = SelectedTip,
                        OdemeDurumu = SelectedOdemeDurumu,
                        DaireId = _currentDaire?.Id ?? string.Empty
                    };
                }

                if (_currentDaire != null)
                {
                    // Ödemeyi Firestore'a kaydet
                    await _firebaseService.FirestoreDb
                        .Collection("Sites")
                        .Document(GetSiteIdFromDaire())
                        .Collection("Apartments")
                        .Document(_currentDaire.ApartmentId)
                        .Collection("Daireler")
                        .Document(_currentDaire.Id)
                        .Collection("Payments")
                        .Document(payment.Id)
                        .SetAsync(payment);

                    // Daire ödeme durumlarını güncelle
                    await UpdateDairePaymentStatusAsync();

                    string action = _isEditMode ? "güncellendi" : "eklendi";
                    Log.Information("Ödeme {Action}: {Tip} - {Tutar:C}", action, SelectedTip, Tutar);
                    
                    MessageBox.Show($"Ödeme başarıyla {action}!", "Başarılı", 
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    PaymentSaved?.Invoke(this, EventArgs.Empty);
                    CloseWindow();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Ödeme kaydedilirken hata oluştu");
                MessageBox.Show($"Kaydetme sırasında hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            CloseWindow();
        }

        private bool ValidateInput()
        {
            if (Tutar <= 0)
            {
                MessageBox.Show("Tutar 0'dan büyük olmalıdır.", "Uyarı", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (Tarih > DateTime.Now)
            {
                MessageBox.Show("Ödeme tarihi gelecek bir tarih olamaz.", "Uyarı", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private async Task UpdateDairePaymentStatusAsync()
        {
            if (_currentDaire == null) return;

            try
            {
                // Son ödemeleri kontrol et ve daire durumlarını güncelle
                var paymentsCollection = _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(GetSiteIdFromDaire())
                    .Collection("Apartments")
                    .Document(_currentDaire.ApartmentId)
                    .Collection("Daireler")
                    .Document(_currentDaire.Id)
                    .Collection("Payments");

                // Son kira ödemesini bul
                var kiraQuery = paymentsCollection
                    .WhereEqualTo("Tip", PaymentType.Kira)
                    .WhereEqualTo("OdemeDurumu", PaymentStatus.Odendi)
                    .OrderByDescending("Tarih")
                    .Limit(1);

                var kiraSnapshot = await kiraQuery.GetSnapshotAsync();
                
                // Son aidat ödemesini bul
                var aidatQuery = paymentsCollection
                    .WhereEqualTo("Tip", PaymentType.Aidat)
                    .WhereEqualTo("OdemeDurumu", PaymentStatus.Odendi)
                    .OrderByDescending("Tarih")
                    .Limit(1);

                var aidatSnapshot = await aidatQuery.GetSnapshotAsync();

                // Durumları güncelle (basit mantık: bu ay ödeme varsa ödendi, yoksa borçlu)
                var thisMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
                
                _currentDaire.KiraDurumu = PaymentStatus.Borclu;
                _currentDaire.AidatDurumu = PaymentStatus.Borclu;

                if (kiraSnapshot.Documents.Count > 0)
                {
                    var lastKira = kiraSnapshot.Documents[0].ConvertTo<Payment>();
                    if (lastKira.Tarih >= thisMonth)
                        _currentDaire.KiraDurumu = PaymentStatus.Odendi;
                }

                if (aidatSnapshot.Documents.Count > 0)
                {
                    var lastAidat = aidatSnapshot.Documents[0].ConvertTo<Payment>();
                    if (lastAidat.Tarih >= thisMonth)
                        _currentDaire.AidatDurumu = PaymentStatus.Odendi;
                }

                // Daire bilgilerini güncelle
                await _firebaseService.FirestoreDb
                    .Collection("Sites")
                    .Document(GetSiteIdFromDaire())
                    .Collection("Apartments")
                    .Document(_currentDaire.ApartmentId)
                    .Collection("Daireler")
                    .Document(_currentDaire.Id)
                    .SetAsync(_currentDaire);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Daire ödeme durumu güncellenirken hata oluştu");
            }
        }

        private string GetSiteIdFromDaire()
        {
            // Bu bilgiyi daire'den alamıyoruz, bu yüzden navigation parametresi olarak geçmemiz gerekiyor
            // Şimdilik basit bir çözüm kullanacağız
            return "default-site-id"; // Bu gerçek implementasyonda düzeltilecek
        }

        private void CloseWindow()
        {
            foreach (Window window in Application.Current.Windows)
            {
                if (window.GetType().Name == "PaymentEditWindow")
                {
                    window.Close();
                    break;
                }
            }
        }
    }
}
