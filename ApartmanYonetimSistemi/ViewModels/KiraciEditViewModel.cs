using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;
using System.Threading.Tasks;
using System.Windows;
using Serilog;
using System;

namespace ApartmanYonetimSistemi.ViewModels
{
    public partial class KiraciEditViewModel : ObservableObject
    {
        private readonly FirebaseService _firebaseService;
        private Daire? _currentDaire;
        private bool _isEditMode;

        [ObservableProperty]
        private string ad = string.Empty;

        [ObservableProperty]
        private string soyad = string.Empty;

        [ObservableProperty]
        private string telefon = string.Empty;

        [ObservableProperty]
        private string email = string.Empty;

        [ObservableProperty]
        private DateTime girisTarihi = DateTime.Now;

        [ObservableProperty]
        private bool isLoading = false;

        [ObservableProperty]
        private string windowTitle = "Yeni Kiracı";

        [ObservableProperty]
        private string saveButtonText = "Kaydet";

        public event EventHandler? KiraciSaved;

        public KiraciEditViewModel()
        {
            _firebaseService = FirebaseService.Instance;
        }

        public void Initialize(Daire daire, Kiraci? kiraci)
        {
            _currentDaire = daire;
            _isEditMode = kiraci != null;

            if (_isEditMode && kiraci != null)
            {
                // Düzenleme modu
                Ad = kiraci.Ad;
                Soyad = kiraci.Soyad;
                Telefon = kiraci.Telefon;
                Email = kiraci.Email;
                GirisTarihi = kiraci.GirisTarihi;
                WindowTitle = "Kiracı Düzenle";
                SaveButtonText = "Güncelle";
            }
            else
            {
                // Yeni kiracı modu
                WindowTitle = "Yeni Kiracı";
                SaveButtonText = "Kaydet";
                GirisTarihi = DateTime.Now;
            }
        }

        [RelayCommand]
        private async Task SaveAsync()
        {
            if (!ValidateInput()) return;

            IsLoading = true;
            try
            {
                var kiraci = new Kiraci
                {
                    Ad = Ad.Trim(),
                    Soyad = Soyad.Trim(),
                    Telefon = Telefon.Trim(),
                    Email = Email.Trim(),
                    GirisTarihi = GirisTarihi,
                    CikisTarihi = null
                };

                if (_currentDaire != null)
                {
                    _currentDaire.Kiraci = kiraci;

                    await _firebaseService.FirestoreDb
                        .Collection("Sites")
                        .Document(GetSiteIdFromDaire())
                        .Collection("Apartments")
                        .Document(_currentDaire.ApartmentId)
                        .Collection("Daireler")
                        .Document(_currentDaire.Id)
                        .SetAsync(_currentDaire);

                    string action = _isEditMode ? "güncellendi" : "eklendi";
                    Log.Information("Kiracı {Action}: {Ad} {Soyad}", action, Ad, Soyad);
                    
                    MessageBox.Show($"Kiracı başarıyla {action}!", "Başarılı", 
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    KiraciSaved?.Invoke(this, EventArgs.Empty);
                    CloseWindow();
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Kiracı kaydedilirken hata oluştu");
                MessageBox.Show($"Kaydetme sırasında hata oluştu: {ex.Message}", "Hata", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        [RelayCommand]
        private void Cancel()
        {
            CloseWindow();
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(Ad))
            {
                MessageBox.Show("Ad alanı boş bırakılamaz.", "Uyarı", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(Soyad))
            {
                MessageBox.Show("Soyad alanı boş bırakılamaz.", "Uyarı", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(Telefon))
            {
                MessageBox.Show("Telefon alanı boş bırakılamaz.", "Uyarı", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (!string.IsNullOrWhiteSpace(Email) && !IsValidEmail(Email))
            {
                MessageBox.Show("Geçerli bir email adresi girin.", "Uyarı", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (GirisTarihi > DateTime.Now)
            {
                MessageBox.Show("Giriş tarihi gelecek bir tarih olamaz.", "Uyarı", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private string GetSiteIdFromDaire()
        {
            // Bu bilgiyi daire'den alamıyoruz, bu yüzden navigation parametresi olarak geçmemiz gerekiyor
            // Şimdilik basit bir çözüm kullanacağız
            return "default-site-id"; // Bu gerçek implementasyonda düzeltilecek
        }

        private void CloseWindow()
        {
            foreach (Window window in Application.Current.Windows)
            {
                if (window.GetType().Name == "KiraciEditWindow")
                {
                    window.Close();
                    break;
                }
            }
        }
    }
}
