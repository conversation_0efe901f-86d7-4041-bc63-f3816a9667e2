<Window x:Class="ApartmanYonetimSistemi.Views.PaymentEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
        Title="{Binding WindowTitle}" 
        Height="450" Width="450"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Window.DataContext>
        <vm:PaymentEditViewModel />
    </Window.DataContext>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2E86AB" Padding="20,15">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                <TextBlock Text="💰" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Text="{Binding WindowTitle}" 
                          FontSize="18" FontWeight="Bold" 
                          Foreground="White"/>
            </StackPanel>
        </Border>

        <!-- Form -->
        <Border Grid.Row="1" Background="White" Margin="30,20,30,20" 
                CornerRadius="8" Padding="30"
                Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
            <StackPanel>
                
                <!-- Ödeme Tipi -->
                <TextBlock Text="Ödeme Tipi *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox ItemsSource="{Binding PaymentTypes}" 
                          SelectedItem="{Binding SelectedTip}"
                          Height="35" FontSize="14" Padding="10,8"
                          BorderBrush="#CCCCCC" BorderThickness="1" Margin="0,0,0,15">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- Tutar -->
                <TextBlock Text="Tutar *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox Text="{Binding Tutar, UpdateSourceTrigger=PropertyChanged}" 
                         Height="35" FontSize="14" Padding="10,8"
                         BorderBrush="#CCCCCC" BorderThickness="1" Margin="0,0,0,15"/>

                <!-- Tarih -->
                <TextBlock Text="Ödeme Tarihi *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <DatePicker SelectedDate="{Binding Tarih}" 
                           Height="35" FontSize="14" Padding="10,8"
                           BorderBrush="#CCCCCC" BorderThickness="1" Margin="0,0,0,15"/>

                <!-- Ödeme Durumu -->
                <TextBlock Text="Ödeme Durumu *" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <ComboBox ItemsSource="{Binding PaymentStatuses}" 
                          SelectedItem="{Binding SelectedOdemeDurumu}"
                          Height="35" FontSize="14" Padding="10,8"
                          BorderBrush="#CCCCCC" BorderThickness="1" Margin="0,0,0,15">
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                </ComboBox>

                <!-- Required Fields Note -->
                <TextBlock Text="* Zorunlu alanlar" FontSize="11" Foreground="#666666" 
                          FontStyle="Italic" HorizontalAlignment="Right"/>

            </StackPanel>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="White" Padding="30,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Loading Indicator -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="⏳" FontSize="16" Margin="0,0,10,0"/>
                    <TextBlock Text="Kaydediliyor..." FontSize="12" Foreground="#666666"/>
                </StackPanel>

                <!-- Cancel Button -->
                <Button Grid.Column="1" Content="İptal" 
                        Command="{Binding CancelCommand}"
                        Background="#9E9E9E" Foreground="White"
                        BorderThickness="0" Padding="20,10"
                        FontSize="14" Cursor="Hand" Margin="0,0,10,0"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#757575"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- Save Button -->
                <Button Grid.Column="2" Content="{Binding SaveButtonText}" 
                        Command="{Binding SaveCommand}"
                        Background="#4CAF50" Foreground="White"
                        BorderThickness="0" Padding="20,10"
                        FontSize="14" FontWeight="SemiBold" Cursor="Hand"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#45A049"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>
    </Grid>
</Window>
