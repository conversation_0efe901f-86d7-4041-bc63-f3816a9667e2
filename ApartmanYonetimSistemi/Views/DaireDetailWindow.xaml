<Window x:Class="ApartmanYonetimSistemi.Views.DaireDetailWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
        Title="{Binding DaireTitle}" 
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen">
    
    <Window.DataContext>
        <vm:DaireDetailViewModel />
    </Window.DataContext>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2E86AB" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Back Button -->
                <Button Grid.Column="0" Content="← Geri" 
                        Command="{Binding GoBackCommand}"
                        Background="#1976D2" Foreground="White"
                        BorderThickness="0" Padding="15,8"
                        FontSize="12" Cursor="Hand" Margin="0,0,20,0">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#1565C0"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- Daire Info -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🚪" FontSize="24" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="{Binding DaireTitle}" 
                                  FontSize="20" FontWeight="Bold" 
                                  Foreground="White"/>
                        <TextBlock Text="Daire Yönetimi ve Ödeme Takibi" 
                                  FontSize="14" 
                                  Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>

                <!-- Actions -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <Button Content="💰 Ödeme Ekle" 
                            Command="{Binding AddPaymentCommand}"
                            Background="#FF9800" Foreground="White"
                            BorderThickness="0" Padding="12,8"
                            FontSize="11" FontWeight="SemiBold" Cursor="Hand" Margin="0,0,10,0">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" 
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#F57C00"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                    
                    <Button Content="⚙️ Daire Ayarları" 
                            Command="{Binding UpdateDaireInfoCommand}"
                            Background="#607D8B" Foreground="White"
                            BorderThickness="0" Padding="12,8"
                            FontSize="11" FontWeight="SemiBold" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" 
                                                                VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#455A64"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Daire ve Kiracı Bilgileri -->
            <StackPanel Grid.Column="0" Margin="0,0,20,0">
                
                <!-- Daire Bilgileri -->
                <Border Background="White" CornerRadius="8" Padding="20" Margin="0,0,0,20"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel>
                        <TextBlock Text="📋 Daire Bilgileri" FontSize="16" FontWeight="SemiBold" 
                                  Foreground="#2E86AB" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Daire No:" FontWeight="SemiBold" Margin="0,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding CurrentDaire.DaireNo}" Margin="0,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Kira Bedeli:" FontWeight="SemiBold" Margin="0,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding CurrentDaire.KiraBedeli, StringFormat={}{0:C}}" Margin="0,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Aidat:" FontWeight="SemiBold" Margin="0,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding CurrentDaire.Aidat, StringFormat={}{0:C}}" Margin="0,5"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Durum:" FontWeight="SemiBold" Margin="0,5"/>
                            <StackPanel Grid.Row="3" Grid.Column="1" Margin="0,5">
                                <TextBlock Text="{Binding CurrentDaire.KiraDurumu, StringFormat={}Kira: {0}}" FontSize="11"/>
                                <TextBlock Text="{Binding CurrentDaire.AidatDurumu, StringFormat={}Aidat: {0}}" FontSize="11"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Kiracı Bilgileri -->
                <Border Background="White" CornerRadius="8" Padding="20"
                        Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                    <StackPanel>
                        <Grid Margin="0,0,0,15">
                            <TextBlock Text="👤 Kiracı Bilgileri" FontSize="16" FontWeight="SemiBold" 
                                      Foreground="#2E86AB" HorizontalAlignment="Left"/>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                                <Button Content="✏️" Command="{Binding EditKiraciCommand}"
                                        Visibility="{Binding HasKiraci, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        Background="#4CAF50" Foreground="White" BorderThickness="0" 
                                        Width="30" Height="25" FontSize="10" Cursor="Hand" Margin="0,0,5,0"/>
                                <Button Content="❌" Command="{Binding RemoveKiraciCommand}"
                                        Visibility="{Binding HasKiraci, Converter={StaticResource BooleanToVisibilityConverter}}"
                                        Background="#F44336" Foreground="White" BorderThickness="0" 
                                        Width="30" Height="25" FontSize="10" Cursor="Hand"/>
                            </StackPanel>
                        </Grid>

                        <!-- Kiracı Var -->
                        <StackPanel Visibility="{Binding HasKiraci, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <TextBlock Text="{Binding CurrentDaire.Kiraci.Ad}" FontWeight="SemiBold" FontSize="14"/>
                            <TextBlock Text="{Binding CurrentDaire.Kiraci.Soyad}" FontWeight="SemiBold" FontSize="14"/>
                            <TextBlock Text="{Binding CurrentDaire.Kiraci.Telefon}" FontSize="12" Margin="0,5,0,0"/>
                            <TextBlock Text="{Binding CurrentDaire.Kiraci.Email}" FontSize="12"/>
                            <TextBlock Text="{Binding CurrentDaire.Kiraci.GirisTarihi, StringFormat={}Giriş: {0:dd.MM.yyyy}}" 
                                      FontSize="11" Foreground="#666666" Margin="0,5,0,0"/>
                        </StackPanel>

                        <!-- Kiracı Yok -->
                        <StackPanel Visibility="{Binding HasKiraci, Converter={StaticResource InverseBooleanConverter}}">
                            <TextBlock Text="Bu daire boş" FontStyle="Italic" Foreground="#666666" 
                                      HorizontalAlignment="Center" Margin="0,20"/>
                            <Button Content="+ Kiracı Ekle" Command="{Binding AddKiraciCommand}"
                                    Background="#4CAF50" Foreground="White" BorderThickness="0" 
                                    Padding="15,8" FontSize="12" FontWeight="SemiBold" Cursor="Hand">
                                <Button.Style>
                                    <Style TargetType="Button">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" 
                                                            CornerRadius="4" Padding="{TemplateBinding Padding}">
                                                        <ContentPresenter HorizontalAlignment="Center" 
                                                                        VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#45A049"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Button.Style>
                            </Button>
                        </StackPanel>
                    </StackPanel>
                </Border>
            </StackPanel>

            <!-- Right Panel - Ödeme Geçmişi -->
            <Border Grid.Column="1" Background="White" CornerRadius="8" Padding="20"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- Header -->
                    <TextBlock Grid.Row="0" Text="💰 Ödeme Geçmişi" FontSize="16" FontWeight="SemiBold" 
                              Foreground="#2E86AB" Margin="0,0,0,15"/>

                    <!-- Loading -->
                    <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center"
                               Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="Ödeme geçmişi yükleniyor..." FontSize="14" 
                                  Foreground="#666666" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>

                    <!-- Ödeme Listesi -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                                 Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                        <ItemsControl ItemsSource="{Binding OdemeGecmisi}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#F8F9FA" BorderBrush="#E0E0E0" BorderThickness="1"
                                            CornerRadius="6" Margin="0,0,0,10" Padding="15" Cursor="Hand">
                                        <Border.InputBindings>
                                            <MouseBinding MouseAction="LeftClick" 
                                                         Command="{Binding DataContext.EditPaymentCommand, 
                                                                  RelativeSource={RelativeSource AncestorType=Window}}"
                                                         CommandParameter="{Binding}"/>
                                        </Border.InputBindings>
                                        
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#E3F2FD"/>
                                                        <Setter Property="BorderBrush" Value="#2E86AB"/>
                                                    </Trigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding Tip}" FontWeight="SemiBold" FontSize="14"/>
                                                <TextBlock Text="{Binding Tarih, StringFormat={}{0:dd.MM.yyyy}}" 
                                                          FontSize="12" Foreground="#666666"/>
                                            </StackPanel>

                                            <TextBlock Grid.Column="1" Text="{Binding Tutar, StringFormat={}{0:C}}" 
                                                      FontWeight="SemiBold" FontSize="14" VerticalAlignment="Center" Margin="10,0"/>

                                            <Border Grid.Column="2" CornerRadius="12" Padding="8,4" VerticalAlignment="Center">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding OdemeDurumu}" Value="Odendi">
                                                                <Setter Property="Background" Value="#4CAF50"/>
                                                            </DataTrigger>
                                                            <DataTrigger Binding="{Binding OdemeDurumu}" Value="Borclu">
                                                                <Setter Property="Background" Value="#F44336"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock Text="{Binding OdemeDurumu}" Foreground="White" 
                                                          FontSize="10" FontWeight="SemiBold"/>
                                            </Border>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>

                    <!-- Empty State -->
                    <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center"
                               Visibility="{Binding OdemeGecmisi.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                        <TextBlock Text="💰" FontSize="48" HorizontalAlignment="Center" Opacity="0.5"/>
                        <TextBlock Text="Henüz ödeme kaydı bulunmuyor" FontSize="16" 
                                  Foreground="#666666" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="Yeni ödeme eklemek için yukarıdaki butonu kullanın" 
                                  FontSize="12" Foreground="#999999" 
                                  HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
