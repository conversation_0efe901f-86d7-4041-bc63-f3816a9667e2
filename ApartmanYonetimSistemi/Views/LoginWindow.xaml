<Window x:Class="ApartmanYonetimSistemi.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
        Title="Apartman Yönetim Sistemi - Giriş" 
        Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    
    <Window.DataContext>
        <vm:LoginViewModel />
    </Window.DataContext>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2E86AB" Padding="20">
            <StackPanel>
                <TextBlock Text="Apartman Yönetim <PERSON>" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="Giriş Yapın" 
                          FontSize="14" 
                          Foreground="White" HorizontalAlignment="Center" 
                          Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- Login Form -->
        <Border Grid.Row="1" Background="White" 
                Margin="50,30,50,30" 
                CornerRadius="10"
                Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
            <StackPanel Margin="40,30,40,30">
                
                <!-- Email -->
                <TextBlock Text="Email:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox Text="{Binding Email, UpdateSourceTrigger=PropertyChanged}" 
                         Height="35" FontSize="14" Padding="10,8"
                         BorderBrush="#CCCCCC" BorderThickness="1"/>

                <!-- Password -->
                <TextBlock Text="Şifre:" FontWeight="SemiBold" Margin="0,20,0,5"/>
                <PasswordBox x:Name="PasswordBox" 
                            Height="35" FontSize="14" Padding="10,8"
                            BorderBrush="#CCCCCC" BorderThickness="1"
                            PasswordChanged="PasswordBox_PasswordChanged"/>

                <!-- Error Message -->
                <TextBlock Text="{Binding ErrorMessage}" 
                          Foreground="Red" FontSize="12" 
                          Margin="0,10,0,0"
                          Visibility="{Binding ErrorMessage, Converter={StaticResource StringToVisibilityConverter}}"
                          TextWrapping="Wrap"/>

                <!-- Login Button -->
                <Button Content="Giriş Yap" 
                        Command="{Binding LoginCommand}"
                        Height="40" FontSize="14" FontWeight="SemiBold"
                        Background="#2E86AB" Foreground="White"
                        BorderThickness="0" Margin="0,20,0,0"
                        IsEnabled="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}"
                        Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="5" Padding="10">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#1F5F8B"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- Loading Indicator -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" 
                           Margin="0,15,0,0"
                           Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <TextBlock Text="Giriş yapılıyor..." FontSize="12" Foreground="#666666"/>
                </StackPanel>

                <!-- Test Info -->
                <Border Background="#F0F8FF" Padding="10" Margin="0,20,0,0" CornerRadius="5">
                    <StackPanel>
                        <TextBlock Text="Test Kullanıcısı:" FontWeight="SemiBold" FontSize="12"/>
                        <TextBlock Text="Email: <EMAIL>" FontSize="11" Margin="0,2,0,0"/>
                        <TextBlock Text="Şifre: 123456" FontSize="11"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </Border>
    </Grid>
</Window>
