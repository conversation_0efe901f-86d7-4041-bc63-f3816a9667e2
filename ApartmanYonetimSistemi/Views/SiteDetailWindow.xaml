<Window x:Class="ApartmanYonetimSistemi.Views.SiteDetailWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:vm="clr-namespace:ApartmanYonetimSistemi.ViewModels"
        Title="{Binding CurrentSite.SiteName, StringFormat='Site Detayı - {0}'}" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    
    <Window.DataContext>
        <vm:SiteDetailViewModel />
    </Window.DataContext>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2E86AB" Padding="20,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Back Button -->
                <Button Grid.Column="0" Content="← Geri" 
                        Command="{Binding GoBackCommand}"
                        Background="#1976D2" Foreground="White"
                        BorderThickness="0" Padding="15,8"
                        FontSize="12" Cursor="Hand" Margin="0,0,20,0">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#1565C0"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>

                <!-- Site Info -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🏢" FontSize="24" Margin="0,0,10,0"/>
                    <StackPanel>
                        <TextBlock Text="{Binding CurrentSite.SiteName}" 
                                  FontSize="20" FontWeight="Bold" 
                                  Foreground="White"/>
                        <TextBlock Text="{Binding CurrentSite.Address}" 
                                  FontSize="14" 
                                  Foreground="#E3F2FD"/>
                    </StackPanel>
                </StackPanel>

                <!-- Actions -->
                <Button Grid.Column="2" Content="+ Yeni Apartman" 
                        Command="{Binding CreateNewApartmentCommand}"
                        Background="#4CAF50" Foreground="White"
                        BorderThickness="0" Padding="15,8"
                        FontSize="12" FontWeight="SemiBold" Cursor="Hand">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="4" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" 
                                                            VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                            <Style.Triggers>
                                <Trigger Property="IsMouseOver" Value="True">
                                    <Setter Property="Background" Value="#45A049"/>
                                </Trigger>
                            </Style.Triggers>
                        </Style>
                    </Button.Style>
                </Button>
            </Grid>
        </Border>

        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="30">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Info Bar -->
            <Border Grid.Row="0" Background="White" 
                    Padding="20,15" Margin="0,0,0,20"
                    CornerRadius="8"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="🏠" FontSize="20" Margin="0,0,10,0"/>
                    <TextBlock Text="Apartman Yönetimi" FontSize="18" FontWeight="SemiBold" 
                              Foreground="#2E86AB" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding Apartments.Count, StringFormat=' ({0} apartman)'}" 
                              FontSize="14" Foreground="#666666" 
                              VerticalAlignment="Center" Margin="10,0,0,0"/>
                </StackPanel>
            </Border>

            <!-- Apartments List -->
            <Border Grid.Row="1" Background="White"
                    CornerRadius="8" Padding="20"
                    Effect="{DynamicResource {x:Static SystemParameters.DropShadowKey}}">

                <Grid>
                    <!-- Loading Indicator -->
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                               Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <TextBlock Text="⏳" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="Apartmanlar yükleniyor..." FontSize="14"
                                  Foreground="#666666" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>

                    <!-- Apartments Grid -->
                    <ScrollViewer VerticalScrollBarVisibility="Auto"
                                 Visibility="{Binding IsLoading, Converter={StaticResource InverseBooleanConverter}}">
                        <ItemsControl ItemsSource="{Binding Apartments}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal"/>
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>
                        
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Background="#F8F9FA" 
                                        BorderBrush="#E0E0E0" BorderThickness="1"
                                        CornerRadius="8" Margin="10" Padding="20"
                                        Width="280" Height="160" Cursor="Hand">
                                    <Border.InputBindings>
                                        <MouseBinding MouseAction="LeftClick" 
                                                     Command="{Binding DataContext.SelectApartmentCommand, 
                                                              RelativeSource={RelativeSource AncestorType=Window}}"
                                                     CommandParameter="{Binding}"/>
                                    </Border.InputBindings>
                                    
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#E3F2FD"/>
                                                    <Setter Property="BorderBrush" Value="#2E86AB"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>

                                    <StackPanel>
                                        <TextBlock Text="🏠" FontSize="32" 
                                                  HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="{Binding ApartmentName}" 
                                                  FontSize="16" FontWeight="SemiBold"
                                                  HorizontalAlignment="Center" 
                                                  TextWrapping="Wrap" Margin="0,0,0,5"/>
                                        <TextBlock Text="Apartman Detayları"
                                                  FontSize="12" Foreground="#666666"
                                                  HorizontalAlignment="Center"
                                                  TextWrapping="Wrap"/>
                                        <TextBlock Text="Detayları Görüntüle →" 
                                                  FontSize="11" Foreground="#2E86AB"
                                                  HorizontalAlignment="Center" 
                                                  Margin="0,10,0,0"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </ScrollViewer>

                    <!-- Empty State -->
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                               Visibility="{Binding Apartments.Count, Converter={StaticResource ZeroToVisibilityConverter}}">
                        <TextBlock Text="🏠" FontSize="48" HorizontalAlignment="Center" Opacity="0.5"/>
                        <TextBlock Text="Henüz apartman bulunmuyor" FontSize="18"
                                  Foreground="#666666" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                        <TextBlock Text="Yeni bir apartman eklemek için yukarıdaki butonu kullanın"
                                  FontSize="12" Foreground="#999999"
                                  HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
