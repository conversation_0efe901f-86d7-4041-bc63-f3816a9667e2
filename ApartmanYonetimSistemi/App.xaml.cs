using System.Configuration;
using System.Data;
using System.Windows;
using Serilog;
using System;

namespace ApartmanYonetimSistemi;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // Serilog yapılandırması
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .WriteTo.Console()
            .WriteTo.File("logs/apartman-yonetim-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        Log.Information("Apartman Yönetim Sistemi başlatılıyor...");

        base.OnStartup(e);
    }

    protected override void OnExit(ExitEventArgs e)
    {
        Log.Information("Apartman Yönetim Sistemi kapatılıyor...");
        Log.CloseAndFlush();
        base.OnExit(e);
    }
}

