using Google.Cloud.Firestore;
using System.Collections.Generic;

namespace ApartmanYonetimSistemi.Models
{
    [FirestoreData]
    public class User
    {
        [FirestoreProperty]
        public string Id { get; set; } = string.Empty;

        [FirestoreProperty]
        public string Email { get; set; } = string.Empty;

        [FirestoreProperty]
        public string Name { get; set; } = string.Empty;

        [FirestoreProperty]
        public UserRole Role { get; set; } = UserRole.Görüntüleyici;

        [FirestoreProperty]
        public List<string> AssignedSites { get; set; } = new List<string>();
    }

    public enum UserRole
    {
        Admin,
        Yönetici,
        Görüntüleyici
    }
}
